# Batch Broadcasting Implementation Guide

## 🎯 Problem Solved

**Race Condition Issue**: WebSocket events for text messages were being dropped when sending mixed batches (text + files) due to:
- Multiple rapid `broadcast_platform_message_update.delay()` calls overwhelming the WebSocket system
- Rate limiting (100/m) in Celery configuration
- Out-of-order execution of broadcast tasks
- WebSocket queue overflow

## 🔧 Backend Implementation

### 1. New Batch Broadcasting Task (`customer/tasks.py`)

```python
@shared_task
def broadcast_message_batch(platform_identity_id: int, message_ids: List[int]):
    """
    Broadcast multiple messages at once to prevent race conditions.
    This replaces individual broadcast_platform_message_update calls for batch message creation.
    """
    # Get all messages with related data in sequence order
    messages = Message.objects.select_related(
        'ticket_id', 'platform_identity', 'created_by', 'message_template'
    ).filter(id__in=message_ids).order_by('sequence_number')
    
    # Broadcast each message in sequence order to WebSocket channels
    for message in messages:
        # Serialize and broadcast to global_platforms and ticket channels
        # ... (full implementation in file)
```

### 2. Transaction-based Broadcasting (`ticket/services/message_creation_service.py`)

```python
def create_message_batch_with_preloaded_files(self, ...):
    with transaction.atomic():
        # Create messages with skip_broadcast=True
        # ...
        
        # Schedule batch broadcast after transaction commits
        if created_messages:
            message_ids = [msg.id for msg in created_messages]
            transaction.on_commit(
                lambda: broadcast_message_batch.delay(platform_identity.id, message_ids)
            )
```

### 3. Enhanced Message Creation Methods

- Added `skip_broadcast: bool = False` parameter to `create_text_message()` and `create_file_message_from_preloaded()`
- Individual broadcasts are skipped during batch creation
- Status update broadcasts (DELIVERED/FAILED) are preserved in `_route_and_update_message()`

## 🌐 Frontend Implementation

### 1. Enhanced WebSocket Handler (`src/lib/websocket/platformWebSocket.ts`)

```typescript
private handleMessage(message: WebSocketMessage) {
    switch (message.type) {
        case 'platform_message_update':
            this.handlePlatformMessageUpdate(message);
            break;
        case 'batch_complete':
            this.handleBatchComplete(message);
            break;
        // ... other cases
    }
}

private handlePlatformMessageUpdate(message: any) {
    // Handle the new batch broadcasting events
    window.dispatchEvent(new CustomEvent('platform-new-message', { 
        detail: {
            platformId: message.platform_id,
            message: message.message,
            unreadCount: message.unread_count,
            updateType: message.update_type,
            batchId: message.batch_id
        }
    }));
}
```

### 2. Enhanced Component Event Handling

#### PlatformIdentityList.svelte
- Added `platform-batch-complete` event listener
- Enhanced `handleNewMessage()` to handle batch information
- Added `handleBatchComplete()` for batch completion events

#### ConversationView.svelte
- Enhanced `handleNewMessage()` to log batch information
- Maintains existing message reading behavior

### 3. Message Store Integration

The existing `conversationStore.addMessage()` already handles:
- Duplicate message prevention
- Message ordering
- Reactive updates

## 🚀 How It Works Now

### Before (Race Condition):
1. Send "text + 3 files"
2. 4+ individual `broadcast_platform_message_update.delay()` calls
3. Race conditions due to rate limiting
4. Messages dropped or received out of order

### After (Batch Broadcasting):
1. Send "text + 3 files"
2. Messages created with `skip_broadcast=True`
3. Single `broadcast_message_batch.delay()` after transaction commit
4. Messages broadcast in sequence order
5. All 4 messages reliably received by frontend

## 📊 Event Flow

```
Backend Batch Creation
├── create_text_message(skip_broadcast=True)
├── create_file_message_from_preloaded(skip_broadcast=True) × 3
├── transaction.on_commit()
└── broadcast_message_batch.delay()
    ├── Serialize messages in sequence order
    ├── Broadcast to 'global_platforms' channel
    └── Broadcast to ticket-specific channels

Frontend Reception
├── platformWebSocket receives 'platform_message_update' events
├── Dispatches 'platform-new-message' custom events
├── PlatformIdentityList.handleNewMessage() processes each message
├── conversationStore.addMessage() adds to conversation
└── MessageList displays messages in correct sequence
```

## 🧪 Testing

To verify the implementation:

1. **Send mixed batch**: Text + multiple files through message creation endpoint
2. **Monitor WebSocket events**: Check browser developer tools for WebSocket messages
3. **Verify message sequence**: All messages should appear in correct order
4. **Check Celery logs**: Confirm batch broadcasting is working
5. **Test race condition fix**: No dropped messages under load

## 🔄 Backward Compatibility

- Existing individual message broadcasts still work for non-batch scenarios
- Status update broadcasts (DELIVERED/FAILED) remain unchanged
- Frontend components handle both old and new event formats
- No breaking changes to existing API endpoints

## 📝 Key Benefits

1. **Race Condition Fixed**: No more dropped messages in mixed batches
2. **Improved Performance**: Single batch broadcast vs multiple individual broadcasts
3. **Better Sequencing**: Messages guaranteed to be broadcast in correct order
4. **Preserved Functionality**: Status updates and individual messages still work
5. **Enhanced Logging**: Better debugging with batch information

The implementation successfully resolves the WebSocket race condition while maintaining all existing functionality and improving the overall message broadcasting system.
